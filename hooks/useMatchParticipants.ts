import { useState, useCallback, useEffect } from 'react';
import { useFocusEffect } from '@react-navigation/native';
import {
  ParticipantType,
  ParticipantDetails,
  PARTICIPANT_TYPES,
  TeamParticipant,
  PlayerParticipant,
  PairParticipant,
} from '../types/participants';
import { fetchTeamById, fetchPairById } from '../services/teamsService';
import { fetchPlayerById } from '../services/playerService';

interface UseMatchParticipantsProps {
  participant1Id: string | null;
  participant2Id: string | null;
  participantType: ParticipantType;
  participant1Name?: string | null;
  participant2Name?: string | null;
  enableCache?: boolean;
  cacheExpiryMs?: number;
}

interface UseMatchParticipantsReturn {
  participant1: ParticipantDetails | null;
  participant2: ParticipantDetails | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

// Cache for participant data
const participantCache: { [key: string]: { data: ParticipantDetails; timestamp: number } } = {};

const participantServices = {
  [PARTICIPANT_TYPES.TEAM]: {
    fetch: fetchTeamById,
    transform: (data: any): TeamParticipant => ({
      id: data.team.id,
      name: data.team.name,
      type: 'team',
      short_name: data.team.short_name,
      logo_url: data.team.logo_url,
      captain_id: data.team.captain_id,
    }),
    dataKey: 'team',
  },
  [PARTICIPANT_TYPES.PLAYER]: {
    fetch: fetchPlayerById,
    transform: (data: any): PlayerParticipant => ({
      id: data.player.id,
      name: data.player.name,
      type: 'player',
      jersey_number: data.player.jersey_number,
      team_id: data.player.team_id,
    }),
    dataKey: 'player',
  },
  [PARTICIPANT_TYPES.PAIR]: {
    fetch: fetchPairById,
    transform: (data: any): PairParticipant => ({
      id: data.data.id,
      name: data.data.name,
      type: 'pair',
      player_1_name: data.data.player_1_name,
      player_2_name: data.data.player_2_name,
      player_1_id: data.data.player_1_id,
      player_2_id: data.data.player_2_id,
    }),
    dataKey: 'data',
  },
};

const createFallbackParticipant = (
  name: string,
  type: ParticipantType
): ParticipantDetails | null => {
  switch (type) {
    case PARTICIPANT_TYPES.TEAM:
      return { id: 'fallback', name, type: 'team' };

    case PARTICIPANT_TYPES.PLAYER:
      return { id: 'fallback', name, type: 'player' };

    case PARTICIPANT_TYPES.PAIR:
      const [player1, player2] = name.split(/\s*[/&]\s*/);
      return {
        id: 'fallback',
        name,
        type: 'pair',
        player_1_name: player1 || name,
        player_2_name: player2 || name,
      };

    default:
      return null;
  }
};

export const useMatchParticipants = ({
  participant1Id,
  participant2Id,
  participantType,
  participant1Name,
  participant2Name,
  enableCache = true,
  cacheExpiryMs = 5 * 60 * 1000, // 5 minutes default
}: UseMatchParticipantsProps): UseMatchParticipantsReturn => {
  const [participant1, setParticipant1] = useState<ParticipantDetails | null>(null);
  const [participant2, setParticipant2] = useState<ParticipantDetails | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const getCacheKey = useCallback((id: string, type: ParticipantType) => {
    return `${type}_${id}`;
  }, []);

  const isCacheValid = useCallback(
    (cacheKey: string) => {
      if (!enableCache || !participantCache[cacheKey]) return false;
      const { timestamp } = participantCache[cacheKey];
      return Date.now() - timestamp < cacheExpiryMs;
    },
    [enableCache, cacheExpiryMs]
  );

  const getFromCache = useCallback(
    (cacheKey: string): ParticipantDetails | null => {
      if (!enableCache || !isCacheValid(cacheKey)) return null;
      return participantCache[cacheKey].data;
    },
    [enableCache, isCacheValid]
  );

  const setToCache = useCallback(
    (cacheKey: string, data: ParticipantDetails) => {
      if (!enableCache) return;
      participantCache[cacheKey] = {
        data,
        timestamp: Date.now(),
      };
    },
    [enableCache]
  );

  const fetchParticipantById = async (
    participantId: string,
    participantName: string | null
  ): Promise<ParticipantDetails | null> => {
    const cacheKey = getCacheKey(participantId, participantType);
    
    // Check cache first
    const cachedData = getFromCache(cacheKey);
    if (cachedData) {
      return cachedData;
    }

    try {
      const service = participantServices[participantType];
      
      if (!service) {
        throw new Error(`Unknown participant type: ${participantType}`);
      }

      const result = await service.fetch(participantId);

      if (result?.success && result[service.dataKey as keyof typeof result]) {
        const transformedParticipant = service.transform(result);
        
        // Cache the result
        setToCache(cacheKey, transformedParticipant);
        
        return transformedParticipant;
      } else {
        // Return fallback participant if fetch fails
        return participantName 
          ? createFallbackParticipant(participantName, participantType)
          : null;
      }
    } catch (err: any) {
      // Return fallback participant if fetch fails
      return participantName 
        ? createFallbackParticipant(participantName, participantType)
        : null;
    }
  };

  const fetchParticipantDetails = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const promises: Promise<ParticipantDetails | null>[] = [];

      // Fetch participant 1
      if (participant1Id) {
        promises.push(fetchParticipantById(participant1Id, participant1Name || null));
      } else {
        promises.push(
          Promise.resolve(
            participant1Name 
              ? createFallbackParticipant(participant1Name, participantType)
              : null
          )
        );
      }

      // Fetch participant 2
      if (participant2Id) {
        promises.push(fetchParticipantById(participant2Id, participant2Name || null));
      } else {
        promises.push(
          Promise.resolve(
            participant2Name 
              ? createFallbackParticipant(participant2Name, participantType)
              : null
          )
        );
      }

      const [p1, p2] = await Promise.all(promises);
      
      setParticipant1(p1);
      setParticipant2(p2);
    } catch (err: any) {
      setError('Failed to fetch participant details');
      // Set fallback participants
      setParticipant1(
        participant1Name 
          ? createFallbackParticipant(participant1Name, participantType)
          : null
      );
      setParticipant2(
        participant2Name 
          ? createFallbackParticipant(participant2Name, participantType)
          : null
      );
    } finally {
      setLoading(false);
    }
  }, [
    participant1Id,
    participant2Id,
    participantType,
    participant1Name,
    participant2Name,
    getCacheKey,
    getFromCache,
    setToCache,
  ]);

  useFocusEffect(
    useCallback(() => {
      fetchParticipantDetails();
    }, [fetchParticipantDetails])
  );

  return {
    participant1,
    participant2,
    loading,
    error,
    refetch: fetchParticipantDetails,
  };
};
