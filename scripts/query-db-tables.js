const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://gkuxmcbgwbermwexhgln.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdrdXhtY2Jnd2Jlcm13ZXhoZ2xuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY5MzcxNzAsImV4cCI6MjA2MjUxMzE3MH0.fwPtJtLMViCiqTF3DuEWbFrMsyicEn8cOFPXzREKfEk';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function queryDatabaseTables() {
  try {
    console.log('🔍 Querying Supabase database for table information...\n');

    // Query to get all tables in the public schema
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name, table_type')
      .eq('table_schema', 'public')
      .order('table_name');

    if (tablesError) {
      console.error('❌ Error fetching tables:', tablesError.message);
      
      // Fallback: Try to query known tables directly
      console.log('\n🔄 Trying fallback method to check known tables...\n');
      
      const knownTables = ['tournaments', 'teams', 'players', 'matches'];
      
      for (const tableName of knownTables) {
        try {
          const { data, error } = await supabase
            .from(tableName)
            .select('*')
            .limit(1);
            
          if (!error) {
            console.log(`✅ Table exists: ${tableName}`);
            
            // Get column information by examining the first row
            if (data && data.length > 0) {
              const columns = Object.keys(data[0]);
              console.log(`   Columns: ${columns.join(', ')}`);
            }
          } else {
            console.log(`❌ Table not accessible: ${tableName} - ${error.message}`);
          }
        } catch (err) {
          console.log(`❌ Error checking table ${tableName}:`, err.message);
        }
      }
      return;
    }

    if (!tables || tables.length === 0) {
      console.log('❌ No tables found or insufficient permissions');
      return;
    }

    console.log(`📊 Found ${tables.length} tables/views:\n`);

    // Group by type
    const regularTables = tables.filter(t => t.table_type === 'BASE TABLE');
    const views = tables.filter(t => t.table_type === 'VIEW');

    if (regularTables.length > 0) {
      console.log('📋 **TABLES:**');
      regularTables.forEach(table => {
        console.log(`  • ${table.table_name}`);
      });
      console.log('');
    }

    if (views.length > 0) {
      console.log('👁️  **VIEWS:**');
      views.forEach(view => {
        console.log(`  • ${view.table_name}`);
      });
      console.log('');
    }

    // Try to get column information for each table
    console.log('🔍 **DETAILED TABLE INFORMATION:**\n');
    
    for (const table of tables) {
      try {
        console.log(`📋 **${table.table_name}** (${table.table_type})`);
        
        // Query column information
        const { data: columns, error: columnsError } = await supabase
          .from('information_schema.columns')
          .select('column_name, data_type, is_nullable, column_default')
          .eq('table_schema', 'public')
          .eq('table_name', table.table_name)
          .order('ordinal_position');

        if (columnsError) {
          console.log(`   ❌ Could not fetch column info: ${columnsError.message}`);
        } else if (columns && columns.length > 0) {
          columns.forEach(col => {
            const nullable = col.is_nullable === 'YES' ? 'nullable' : 'not null';
            const defaultVal = col.column_default ? ` (default: ${col.column_default})` : '';
            console.log(`   • ${col.column_name}: ${col.data_type} (${nullable})${defaultVal}`);
          });
        } else {
          console.log('   ❌ No column information available');
        }
        
        console.log('');
      } catch (err) {
        console.log(`   ❌ Error getting details for ${table.table_name}:`, err.message);
        console.log('');
      }
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error.message);
  }
}

// Run the query
queryDatabaseTables();
