const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://gkuxmcbgwbermwexhgln.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdrdXhtY2Jnd2Jlcm13ZXhoZ2xuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY5MzcxNzAsImV4cCI6MjA2MjUxMzE3MH0.fwPtJtLMViCiqTF3DuEWbFrMsyicEn8cOFPXzREKfEk';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function checkTables() {
  console.log('🔍 Checking database tables...\n');

  const knownTables = [
    'tournaments', 
    'teams', 
    'players', 
    'matches',
    'user_tournaments_with_status'
  ];
  
  for (const tableName of knownTables) {
    try {
      console.log(`📋 Checking table: ${tableName}`);
      
      const { data, error, count } = await supabase
        .from(tableName)
        .select('*', { count: 'exact', head: true });
        
      if (error) {
        console.log(`   ❌ Error: ${error.message}`);
      } else {
        console.log(`   ✅ Table exists with ${count || 0} rows`);
        
        // Get a sample row to see columns
        const { data: sample } = await supabase
          .from(tableName)
          .select('*')
          .limit(1);
          
        if (sample && sample.length > 0) {
          const columns = Object.keys(sample[0]);
          console.log(`   📝 Columns (${columns.length}): ${columns.join(', ')}`);
        }
      }
      console.log('');
    } catch (err) {
      console.log(`   ❌ Exception: ${err.message}`);
      console.log('');
    }
  }
}

checkTables().catch(console.error);
