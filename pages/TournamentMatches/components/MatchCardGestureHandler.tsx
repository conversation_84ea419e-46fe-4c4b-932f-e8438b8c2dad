import React, { memo, useState } from "react";
import { View } from "react-native";
import { TapGestureHandler, State } from "react-native-gesture-handler";
import { Card } from "@/components/ui/card";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Icon } from "@/components/ui/icon";
import { Text } from "@/components/ui/text";
import { MapPinnedIcon } from "lucide-react-native";
import { Match } from "../../../types/matches";
import ParticipantDisplay from "./ParticipantDisplay";
import MatchDetailsDialog from "./MatchDetailsDialog";
import { useMatchParticipants } from "../../../hooks/useMatchParticipants";
import dayjs from "dayjs";

export interface MatchCardProps {
  match: Match;
}

// Most advanced solution using react-native-gesture-handler
const MatchCardGestureHandler: React.FC<MatchCardProps> = ({ match }) => {
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);

  const { participant1, participant2 } = useMatchParticipants({
    participant1Id: match.participant_1_id,
    participant2Id: match.participant_2_id,
    participantType: match.participant_type,
    participant1Name: match.participant_1_name,
    participant2Name: match.participant_2_name,
  });

  const handlePress = () => {
    console.log("MatchCard Pressed - Match ID:", match.id);
    setIsDetailsDialogOpen(true);
  };

  const onTapGestureEvent = (event: any) => {
    if (event.nativeEvent.state === State.END) {
      handlePress();
    }
  };

  const statusColor = match?.status === "scheduled" ? "bg-blue-500" :
                     match?.status === "in_progress" ? "bg-green-500" :
                     match?.status === "completed" ? "bg-gray-500" :
                     match?.status === "cancelled" ? "bg-red-500" : "bg-gray-400";

  const statusText = match?.status === "scheduled" ? "Scheduled" :
                    match?.status === "in_progress" ? "Live" :
                    match?.status === "completed" ? "Completed" :
                    match?.status === "cancelled" ? "Cancelled" : 
                    match?.status?.toUpperCase();

  const formatDate = (dateTime: string | null): string => {
    if (!dateTime) return "TBD";
    try {
      return dayjs(dateTime).format("MMM DD, YYYY");
    } catch (error) {
      return "Invalid Date";
    }
  };

  const formatTime = (dateTime: string | null): string | null => {
    if (!dateTime) return null;
    try {
      return dayjs(dateTime).format("h:mm A");
    } catch (error) {
      return "Invalid Time";
    }
  };

  return (
    <>
      <TapGestureHandler
        onHandlerStateChange={onTapGestureEvent}
        maxDurationMs={300}
        maxDeltaX={10}
        maxDeltaY={10}
        numberOfTaps={1}
        shouldCancelWhenOutside={true}
      >
        <View>
          <Card className="bg-white border border-gray-200 rounded-3xl shadow-md">
            <VStack className="p-2">
              <HStack className="items-center w-full">
                <View className="flex-1 max-w-[25%]">
                  <ParticipantDisplay
                    participantId={match.participant_1_id}
                    participantType={match.participant_type}
                    participantName={match.participant_1_name}
                    fallbackName="TBD"
                    preloadedParticipant={participant1}
                  />
                </View>

                <VStack className="items-center px-4 flex-shrink-0 w-[50%]">
                  <View className={`px-3 py-1 rounded-full ${statusColor} mb-2`}>
                    <Text className="text-white text-[9px] font-urbanistBold uppercase">
                      {statusText}
                    </Text>
                  </View>
                  <VStack className="space-y-1 items-center">
                    <Text className="text-gray-600 text-lg font-urbanistBold text-center">
                      {formatDate(match.scheduled_date)}
                    </Text>
                    {formatTime(match.scheduled_date) && (
                      <Text className="text-gray-600 text-sm font-urbanistSemiBold text-center">
                        {formatTime(match.scheduled_date)}
                      </Text>
                    )}
                  </VStack>
                </VStack>

                <View className="flex-1 max-w-[25%]">
                  <ParticipantDisplay
                    participantId={match.participant_2_id}
                    participantType={match.participant_type}
                    participantName={match.participant_2_name}
                    fallbackName="TBD"
                    preloadedParticipant={participant2}
                  />
                </View>
              </HStack>
              {match.court_field_number && (
                <VStack className="space-y-2 pt-2 mt-4 border-t border-gray-200 items-center">
                  <HStack className="items-center space-x-2 gap-1">
                    <Icon
                      as={MapPinnedIcon}
                      size="xs"
                      className="text-typography-600"
                    />
                    <Text className="text-sm text-typography-600 font-urbanistMedium">
                      {match.court_field_number}
                    </Text>
                  </HStack>
                </VStack>
              )}
            </VStack>
          </Card>
        </View>
      </TapGestureHandler>
      {isDetailsDialogOpen && (
        <MatchDetailsDialog
          isOpen={isDetailsDialogOpen}
          onClose={() => setIsDetailsDialogOpen(false)}
          match={match}
          participant1Details={participant1}
          participant2Details={participant2}
        />
      )}
    </>
  );
};

export default memo(MatchCardGestureHandler);
